import React, { useState, useRef, useEffect } from 'react';

interface MACAddressFieldProps {
  value: string;
  onChange: (value: string) => void;
  disabled?: boolean;
  required?: boolean;
  error?: string;
  label: string;
}

/**
 * MAC地址专用输入组件
 * 采用6个分段的格式化输入框，支持键盘导航和复制粘贴
 */
const MACAddressField: React.FC<MACAddressFieldProps> = ({
  value,
  onChange,
  disabled = false,
  required = false,
  error,
  label
}) => {
  const [segments, setSegments] = useState<string[]>(['', '', '', '', '', '']);
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

  // 解析MAC地址字符串为分段数组
  const parseMACAddress = (mac: string): string[] => {
    if (!mac) return ['', '', '', '', '', ''];
    
    // 移除所有分隔符，只保留十六进制字符
    const cleanMac = mac.replace(/[^0-9A-Fa-f]/g, '').toUpperCase();
    
    const result = ['', '', '', '', '', ''];
    for (let i = 0; i < Math.min(cleanMac.length / 2, 6); i++) {
      const start = i * 2;
      const segment = cleanMac.slice(start, start + 2);
      if (segment.length > 0) {
        result[i] = segment;
      }
    }
    return result;
  };

  // 将分段数组转换为MAC地址字符串
  const formatMACAddress = (segs: string[]): string => {
    return segs.join(':');
  };

  // 初始化和同步外部值
  useEffect(() => {
    const newSegments = parseMACAddress(value);
    setSegments(newSegments);
  }, [value]);

  // 处理单个分段的输入变化
  const handleSegmentChange = (index: number, inputValue: string) => {
    // 只允许十六进制字符，自动转换为大写
    const hexValue = inputValue.replace(/[^0-9A-Fa-f]/g, '').toUpperCase();
    
    // 限制输入长度为2位
    const finalValue = hexValue.slice(0, 2);

    const newSegments = [...segments];
    newSegments[index] = finalValue;
    setSegments(newSegments);

    // 更新外部值
    const newValue = formatMACAddress(newSegments);
    onChange(newValue);

    // 自动跳转到下一个输入框
    if (finalValue.length === 2 && index < 5) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  // 处理键盘事件
  const handleKeyDown = (index: number, e: React.KeyboardEvent<HTMLInputElement>) => {
    const target = e.target as HTMLInputElement;
    
    switch (e.key) {
      case ':':
      case '-':
        e.preventDefault();
        if (index < 5) {
          inputRefs.current[index + 1]?.focus();
        }
        break;
      case 'ArrowRight':
        if (target.selectionStart === target.value.length && index < 5) {
          e.preventDefault();
          inputRefs.current[index + 1]?.focus();
        }
        break;
      case 'ArrowLeft':
        if (target.selectionStart === 0 && index > 0) {
          e.preventDefault();
          inputRefs.current[index - 1]?.focus();
        }
        break;
      case 'Backspace':
        if (target.value === '' && index > 0) {
          e.preventDefault();
          inputRefs.current[index - 1]?.focus();
        }
        break;
      case 'Tab':
        // 让Tab键正常工作，不做特殊处理
        break;
    }
  };

  // 处理粘贴事件
  const handlePaste = (index: number, e: React.ClipboardEvent<HTMLInputElement>) => {
    e.preventDefault();
    const pastedText = e.clipboardData.getData('text');
    
    // 尝试解析各种MAC地址格式
    const cleanMac = pastedText.replace(/[^0-9A-Fa-f]/g, '').toUpperCase();
    
    if (cleanMac.length >= 12) {
      // 完整MAC地址
      const newSegments = [];
      for (let i = 0; i < 6; i++) {
        const start = i * 2;
        newSegments.push(cleanMac.slice(start, start + 2));
      }
      
      setSegments(newSegments);
      onChange(formatMACAddress(newSegments));
      
      // 聚焦到最后一个输入框
      inputRefs.current[5]?.focus();
    } else if (cleanMac.length > 0) {
      // 部分MAC地址，从当前位置开始填充
      const newSegments = [...segments];
      let currentIndex = index;
      
      for (let i = 0; i < cleanMac.length && currentIndex < 6; i += 2) {
        const segment = cleanMac.slice(i, i + 2);
        if (segment.length === 2) {
          newSegments[currentIndex] = segment;
          currentIndex++;
        } else if (segment.length === 1 && i === cleanMac.length - 1) {
          // 最后一个字符
          newSegments[currentIndex] = segment;
        }
      }
      
      setSegments(newSegments);
      onChange(formatMACAddress(newSegments));
      
      // 聚焦到下一个空位置
      if (currentIndex < 6) {
        inputRefs.current[currentIndex]?.focus();
      }
    }
  };

  return (
    <>
      <label className="block text-sm font-bold text-gray-700 mb-2">
        {label} {required && <span className="text-red-500">*</span>}
      </label>
      
      <div className="flex items-center space-x-1">
        {segments.map((segment, index) => (
          <React.Fragment key={index}>
            <input
              ref={(el) => (inputRefs.current[index] = el)}
              type="text"
              value={segment}
              onChange={(e) => handleSegmentChange(index, e.target.value)}
              onKeyDown={(e) => handleKeyDown(index, e)}
              onPaste={(e) => handlePaste(index, e)}
              placeholder={['AA', 'BB', 'CC', 'DD', 'EE', 'FF'][index]}
              disabled={disabled}
              className={`w-10 px-1 py-2 text-center border rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors font-mono text-sm ${
                error
                  ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                  : 'border-gray-300'
              }`}
              maxLength={2}
            />
            {index < 5 && (
              <span className="text-gray-400 select-none">:</span>
            )}
          </React.Fragment>
        ))}
      </div>

      {/* 错误信息 */}
      {error && (
        <p className="mt-1 text-sm text-red-600">{error}</p>
      )}
    </>
  );
};

export default MACAddressField;
