import React, { useState, useEffect, useCallback } from 'react';
import { X, Building, ArrowRight } from 'lucide-react';
import { DepartmentCategory } from '../../../../hooks/Inventory/useCategoryTree';
import DepartmentTreeSelect from '../../../../components/DepartmentTreeSelect';

// 对话框属性
interface ChangeDepartmentDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (newDepartmentId: string) => Promise<void>;
  departmentCategories: DepartmentCategory[];
  personName: string;
  currentDepartmentName?: string; // 当前部门名称
  isLoading?: boolean;
}

/**
 * 更改部门对话框
 * 用于更改人员所属部门
 */
const ChangeDepartmentDialog: React.FC<ChangeDepartmentDialogProps> = ({
  isOpen,
  onClose,
  onConfirm,
  departmentCategories,
  personName,
  currentDepartmentName = '',
  isLoading = false
}) => {
  const [selectedDepartmentId, setSelectedDepartmentId] = useState<string>('');
  const [error, setError] = useState<string | null>(null);

  // 将部门树转换为DepartmentTreeSelect需要的格式
  const convertToDepartmentOptions = (categories: DepartmentCategory[]): any[] => {
    return categories.map(category => {
      const option = {
        id: category.id,
        value: category.id,
        label: category.name,
      };

      if (category.children && category.children.length > 0) {
        // 只包含部门节点，过滤掉人员节点
        const departmentChildren = category.children.filter(child => child.id.startsWith('dept-'));
        if (departmentChildren.length > 0) {
          option.children = convertToDepartmentOptions(departmentChildren);
        }
      }

      return option;
    });
  };

  // 获取部门选项 - 直接从一级部门开始，跳过根节点
  const departmentOptions = (() => {
    // 检查是否有根节点
    if (departmentCategories.length > 0 && departmentCategories[0].id === 'all-dept') {
      // 找到根节点的子部门（一级部门）
      const rootNode = departmentCategories[0];
      if (rootNode.children && rootNode.children.length > 0) {
        // 只返回部门节点，过滤掉人员节点
        const departmentChildren = rootNode.children.filter(child => child.id.startsWith('dept-'));
        return convertToDepartmentOptions(departmentChildren);
      }
      return [];
    }
    // 如果没有根节点，返回原始转换结果
    return convertToDepartmentOptions(departmentCategories);
  })();

  // 获取选中部门的名称
  const getSelectedDepartmentName = useCallback(() => {
    if (!selectedDepartmentId) return '';

    // 递归查找部门名称
    const findDepartmentName = (options: any[]): string => {
      for (const option of options) {
        if (option.value === selectedDepartmentId) {
          return option.label;
        }
        if (option.children && option.children.length > 0) {
          const found = findDepartmentName(option.children);
          if (found) return found;
        }
      }
      return '';
    };

    return findDepartmentName(departmentOptions);
  }, [selectedDepartmentId, departmentOptions]);

  // 处理确认按钮点击
  const handleConfirm = async () => {
    try {
      if (!selectedDepartmentId) {
        setError('请选择部门');
        return;
      }

      // 检查是否选择了根节点
      if (selectedDepartmentId === 'all-dept') {
        setError('不能选择根节点');
        return;
      }

      // 检查是否选择了人员节点
      if (selectedDepartmentId.startsWith('person-')) {
        setError('请选择部门节点');
        return;
      }

      // 检查是否与当前部门相同
      const selectedDepartmentName = getSelectedDepartmentName();
      if (selectedDepartmentName === currentDepartmentName) {
        setError('新部门与当前部门相同，无需更改');
        return;
      }

      // 先关闭对话框，因为onConfirm会触发拖拽确认对话框
      onClose();

      // 然后执行确认操作
      onConfirm(selectedDepartmentId).catch(error => {
        console.error('更改部门失败:', error);
      });
    } catch (error) {
      setError(error instanceof Error ? error.message : '更改部门失败');
    }
  };

  // 重置状态
  useEffect(() => {
    if (isOpen) {
      setSelectedDepartmentId('');
      setError(null);
    }
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white rounded-lg shadow-xl w-[450px] max-w-full animate-fadeIn">
        {/* 对话框标题 */}
        <div className="flex items-center justify-between px-5 py-2.5 border-b">
          <div className="flex items-center">
            <Building className="h-5 w-5 text-blue-600 mr-2" />
            <h3 className="text-lg font-medium text-gray-900">更改部门</h3>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500 focus:outline-none"
            disabled={isLoading}
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* 对话框内容 */}
        <div className="px-5 py-5">
          <div className="mb-5">
            <p className="text-gray-700 mb-1 font-medium">
              人员信息
            </p>
            <div className="bg-gray-50 p-3 rounded-md">
              <p className="text-gray-800">
                <span className="font-medium">{personName}</span>
              </p>
              {currentDepartmentName && (
                <div className="flex items-center mt-2 text-sm text-gray-600">
                  <span>当前部门：{currentDepartmentName}</span>
                </div>
              )}
            </div>
          </div>

          {/* 部门选择 */}
          <div className="mb-5">
            <div className="flex items-center justify-between mb-2">
              <label className="block text-sm font-medium text-gray-700">
                选择新部门 <span className="text-red-500">*</span>
              </label>
              {selectedDepartmentId && (
                <div className="flex items-center text-sm text-blue-600">
                  <span>已选择：{getSelectedDepartmentName()}</span>
                </div>
              )}
            </div>
            <DepartmentTreeSelect
              options={departmentOptions}
              value={selectedDepartmentId}
              onChange={setSelectedDepartmentId}
              placeholder="请选择新部门"
              required={true}
              disabled={isLoading}
            />
            {error && (
              <p className="mt-2 text-sm text-red-600 bg-red-50 p-2 rounded-md flex items-start">
                <span className="text-red-500 mr-1">!</span> {error}
              </p>
            )}
          </div>

          {selectedDepartmentId && currentDepartmentName && getSelectedDepartmentName() !== currentDepartmentName && (
            <div className="mb-5 bg-blue-50 p-3 rounded-md flex items-center">
              <div className="flex-1">
                <p className="text-blue-700 text-sm">
                  <span className="font-medium">{personName}</span> 将从 <span className="font-medium">{currentDepartmentName}</span> 移动到 <span className="font-medium">{getSelectedDepartmentName()}</span>
                </p>
              </div>
              <ArrowRight className="h-4 w-4 text-blue-500 ml-2" />
            </div>
          )}
        </div>

        {/* 对话框按钮 */}
        <div className="px-5 py-4 bg-gray-50 flex justify-end space-x-3 rounded-b-lg">
          <button
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            disabled={isLoading}
          >
            取消
          </button>
          <button
            onClick={handleConfirm}
            className={`px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${
              isLoading ? 'opacity-75 cursor-not-allowed' : ''
            } ${!selectedDepartmentId ? 'opacity-50 cursor-not-allowed' : ''}`}
            disabled={isLoading || !selectedDepartmentId}
          >
            {isLoading ? '处理中...' : '确认更改'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ChangeDepartmentDialog;
