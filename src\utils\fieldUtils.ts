/**
 * 字段工具函数
 * 提供字段相关的通用工具方法
 */

import { ExtFieldEditMode } from '../types/inventory';

/**
 * 检测字段名称是否为时间相关字段
 * @param fieldName 字段名称
 * @returns 是否为时间字段
 */
export const isTimeField = (fieldName: string): boolean => {
  // 简化逻辑：只要字段名称中包含"时间"或"日期"就认为是时间字段
  return fieldName.includes('时间') || fieldName.includes('日期');
};

/**
 * 扩展字段定义接口
 */
export interface ExtFieldDefinitionInput {
  name: string;
  type: string;
  required: boolean;
  editMode: ExtFieldEditMode;
  options?: Array<{ code: string; value: string }>;
}

/**
 * 构建添加扩展字段的API请求参数
 * @param subCategoryName 二级分类名称
 * @param field 扩展字段定义
 * @returns API请求参数
 */
export const buildAddExtFieldParams = (
  subCategoryName: string,
  field: ExtFieldDefinitionInput
) => {
  return {
    action: "add_extended_field_def",
    action_params: {
      sub_category_name: subCategoryName,
      field_name: field.name,
      field_type: field.type,
      is_required: field.required ? 1 : 0,
      is_editable: field.editMode === ExtFieldEditMode.SelectOnly ? 0 : 1
    }
  };
};

/**
 * 构建添加扩展字段选项的API请求参数
 * @param subCategoryName 二级分类名称
 * @param fieldName 字段名称
 * @param options 选项数组
 * @returns API请求参数
 */
export const buildAddExtFieldOptionsParams = (
  subCategoryName: string,
  fieldName: string,
  options: Array<{ code: string; value: string }>
) => {
  const optionValues = options.map(option => option.value);

  return {
    action: "batch_add_extended_field_options",
    action_params: {
      sub_category_name: subCategoryName,
      field_name: fieldName,
      option_values: optionValues
    }
  };
};

/**
 * 构建更新扩展字段的API请求参数
 * @param subCategoryName 二级分类名称
 * @param field 扩展字段定义
 * @param newFieldName 新字段名称（可选）
 * @returns API请求参数
 */
export const buildUpdateExtFieldParams = (
  subCategoryName: string,
  field: ExtFieldDefinitionInput,
  newFieldName?: string
) => {
  const actionParams: any = {
    sub_category_name: subCategoryName,
    field_name: field.name,
    field_type: field.type,
    is_required: field.required ? 1 : 0,
    is_editable: field.editMode === ExtFieldEditMode.SelectOnly ? 0 : 1
  };

  // 如果提供了新字段名称且与原字段名不同，则添加到请求参数中
  if (newFieldName && newFieldName !== field.name) {
    actionParams.new_field_name = newFieldName;
  }

  return {
    action: "update_extended_field_def",
    action_params: actionParams
  };
};

/**
 * 构建更新扩展字段选项的API请求参数
 * @param subCategoryName 二级分类名称
 * @param fieldName 字段名称
 * @param options 选项数组
 * @returns API请求参数
 */
export const buildUpdateExtFieldOptionsParams = (
  subCategoryName: string,
  fieldName: string,
  options: Array<{ code: string; value: string }>
) => {
  // 构建选项值数组和是否为默认值
  const optionValues = options.map(option => ({
    value: option.value,
    is_default: 0 // 默认设置为非默认值，可以根据需要修改
  }));

  return {
    action: "batch_update_extended_field_options",
    action_params: {
      sub_category_name: subCategoryName,
      field_name: fieldName,
      option_values: optionValues
    }
  };
};

/**
 * 构建删除扩展字段的API请求参数
 * @param subCategoryName 二级分类名称
 * @param fieldName 字段名称
 * @returns API请求参数
 */
export const buildDeleteExtFieldParams = (
  subCategoryName: string,
  fieldName: string
) => {
  return {
    action: "delete_extended_field_def",
    action_params: {
      sub_category_name: subCategoryName,
      field_name: fieldName
    }
  };
};

/**
 * 格式化时间显示值
 * @param value 时间值
 * @param showTime 是否显示时间部分（默认false，只显示日期）
 * @param showSeconds 是否显示秒（默认true）
 * @returns 格式化后的时间字符串
 */
export const formatTimeValue = (value: any, showTime: boolean = false, showSeconds: boolean = true): string => {
  if (!value || value === '') return '';

  try {
    // 尝试解析为日期
    const date = new Date(value);
    if (!isNaN(date.getTime())) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');

      const baseStr = `${year}-${month}-${day}`;

      if (showTime) {
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        let timeStr = `${hours}:${minutes}`;

        if (showSeconds) {
          const seconds = String(date.getSeconds()).padStart(2, '0');
          timeStr += `:${seconds}`;
        }

        return `${baseStr} ${timeStr}`;
      }

      return baseStr;
    }
  } catch (error) {
    // 如果解析失败，返回原值
    console.warn('时间格式化失败:', error);
  }

  return String(value);
};

/**
 * 将时间值转换为ISO格式
 * @param value 时间值
 * @returns ISO格式的时间字符串或原值
 */
export const convertTimeToISO = (value: any): string => {
  if (!value || value === '') return value;

  try {
    // 如果值已经是有效的日期字符串，直接使用
    const date = new Date(value);
    if (!isNaN(date.getTime())) {
      // 转换为ISO字符串格式，后端通常期望这种格式
      return date.toISOString();
    } else {
      // 如果不是有效日期，保留原值
      return value;
    }
  } catch (error) {
    console.warn(`时间格式转换失败:`, error);
    // 转换失败时保留原值
    return value;
  }
};

/**
 * 将时间值转换为Unix时间戳格式（秒级）
 * @param value 时间值（可以是字符串、Date对象或时间戳）
 * @returns Unix时间戳（秒级）或0（如果转换失败）
 */
export const convertToUnixTimestamp = (value: any): number => {
  if (!value || value === '') return 0;

  try {
    let date: Date;

    // 如果已经是数字（时间戳），直接处理
    if (typeof value === 'number') {
      // 判断是秒级还是毫秒级时间戳
      const timestamp = value.toString().length <= 10 ? value : Math.floor(value / 1000);
      return timestamp;
    }

    // 如果是字符串，尝试解析
    if (typeof value === 'string') {
      // 处理 YYYY-MM-DDTHH:mm 格式，确保按本地时间解析
      if (value.includes('T')) {
        const [datePart, timePart] = value.split('T');
        const [year, month, day] = datePart.split('-').map(Number);
        const [hours, minutes] = timePart.split(':').map(Number);
        date = new Date(year, month - 1, day, hours, minutes, 0);
      } else {
        date = new Date(value);
      }
    } else if (value instanceof Date) {
      date = value;
    } else {
      console.warn('不支持的时间格式:', value);
      return 0;
    }

    if (!isNaN(date.getTime())) {
      // 转换为秒级Unix时间戳
      return Math.floor(date.getTime() / 1000);
    } else {
      console.warn('时间解析失败:', value);
      return 0;
    }
  } catch (error) {
    console.warn(`时间戳转换失败:`, error);
    return 0;
  }
};

/**
 * 解析日期字符串为日期对象
 * @param dateStr 日期字符串 (YYYY-MM-DD格式)
 * @returns Date对象或null
 */
export const parseDate = (dateStr: string): Date | null => {
  if (!dateStr) return null;

  // 尝试解析YYYY-MM-DD格式
  const match = dateStr.match(/^(\d{4})-(\d{2})-(\d{2})$/);
  if (match) {
    const year = parseInt(match[1], 10);
    const month = parseInt(match[2], 10) - 1; // 月份从0开始
    const day = parseInt(match[3], 10);

    const date = new Date(year, month, day);

    // 验证日期是否有效
    if (
      date.getFullYear() === year &&
      date.getMonth() === month &&
      date.getDate() === day
    ) {
      return date;
    }
  }

  return null;
};

/**
 * 格式化日期对象为YYYY-MM-DD字符串
 * @param date Date对象
 * @returns YYYY-MM-DD格式的字符串
 */
export const formatDateToString = (date: Date): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

/**
 * 格式化日期为中文显示格式
 * @param dateStr YYYY-MM-DD格式的日期字符串
 * @returns 中文格式的日期字符串 (YYYY年MM月DD日)
 */
export const formatDateToDisplay = (dateStr: string): string => {
  const date = parseDate(dateStr);
  if (!date) return '';

  return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`;
};
