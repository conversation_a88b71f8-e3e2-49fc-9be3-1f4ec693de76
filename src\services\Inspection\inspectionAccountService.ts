import mitt from 'mitt';
import { BaseService, BaseServiceState, BaseServiceEvents } from '../../services/base/baseService';
import WebSocketManager from '../../utils/websocket';
import TaskManager from '../../utils/taskManager';
import InspectionService from './inspectionService';
import { dataManager, DataManagerConfig } from '../Base/dataManager';

/**
 * 账户信息接口
 */
export interface AccountInfo {
  id: string; // 使用user_id作为唯一标识
  department: string; // 所属部门（从person_departments提取）
  inspector: string; // 巡检人（person_name + person_alias组合显示）
  contact?: string; // 用户名（user_name）
  lastLogin?: string; // 最后登录（暂时为空）
  registerTime?: string; // 注册时间（从register_timestamp解析）
  passwordExpireDate?: string;
  isLocked?: boolean;
  // API返回的原始字段
  person_name?: string; // 人员姓名
  person_alias?: string; // 人员别名/备注
  person_departments?: string[]; // 人员所属部门数组
  user_name?: string; // 移动端登录用户名
  user_id?: number; // 用户ID
  register_timestamp?: number | string; // 原始注册时间戳
  [key: string]: any; // 允许添加其他属性
}

/**
 * 巡检账户服务状态接口
 */
export interface InspectionAccountServiceState extends BaseServiceState {
  accounts: AccountInfo[];
  currentAccount?: AccountInfo;
}

/**
 * 巡检账户服务事件接口
 */
export interface InspectionAccountServiceEvents extends BaseServiceEvents<InspectionAccountServiceState> {
  'accounts-loaded': AccountInfo[];
  'account-added': AccountInfo;
  'account-updated': AccountInfo;
  'account-deleted': string;
  'password-changed': { username: string; success: boolean };
}

/**
 * 数据缓存键常量
 */
const CACHE_KEYS = {
  INSPECTION_ACCOUNTS: 'inspection_accounts'
} as const;

/**
 * 数据管理器配置（单机版优化）
 */
const DATA_CONFIG: DataManagerConfig = {
  maxRetries: 3,
  retryDelay: 1000
};

/**
 * 巡检账户服务类
 * 提供账户管理功能
 */
class InspectionAccountService extends BaseService<InspectionAccountServiceState, InspectionAccountServiceEvents> {
  private static instance: InspectionAccountService;

  /**
   * 获取巡检账户服务实例
   * @returns 巡检账户服务实例
   */
  public static getInstance(): InspectionAccountService {
    if (!InspectionAccountService.instance) {
      InspectionAccountService.instance = new InspectionAccountService();
    }
    return InspectionAccountService.instance;
  }

  /**
   * 构造函数
   * 初始化巡检账户服务
   */
  private constructor() {
    super('AccountTableDll', {
      isLoading: false,
      accounts: []
    });
  }

  /**
   * 获取账户列表（智能缓存）
   */
  public async getAccounts(forceRefresh = false): Promise<AccountInfo[]> {
    try {
      await this.ensureInitialized();

      const fetcher = () => this.fetchAccounts();

      let accounts: AccountInfo[];
      if (forceRefresh) {
        accounts = await dataManager.refreshData(CACHE_KEYS.INSPECTION_ACCOUNTS, fetcher, DATA_CONFIG);
      } else {
        accounts = await dataManager.getData(CACHE_KEYS.INSPECTION_ACCOUNTS, fetcher, DATA_CONFIG);
      }

      // 更新本地状态
      this.updateState({
        accounts: accounts,
        isLoading: false
      });

      this.emitter.emit('accounts-loaded', accounts);
      return accounts;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '获取账户数据失败';
      this.updateState({
        error: errorMessage,
        isLoading: false
      });
      this.emitter.emit('error', errorMessage);
      throw error;
    }
  }

  /**
   * 加载账户列表（强制刷新）
   */
  public async loadAccounts(): Promise<AccountInfo[]> {
    this.updateState({ isLoading: true });
    return this.getAccounts(true);
  }

  /**
   * 初始化数据监听器
   */
  public initializeDataListener(): () => void {
    return dataManager.addListener(CACHE_KEYS.INSPECTION_ACCOUNTS, (cacheItem) => {
      // 只在缓存数据更新时同步到本地状态
      if (cacheItem.data && !cacheItem.loading && !cacheItem.error) {
        this.updateState({
          accounts: cacheItem.data,
          isLoading: false
        });
      }
    });
  }

  /**
   * 获取账户数据
   */
  private async fetchAccounts(): Promise<AccountInfo[]> {
    try {
      await this.ensureInitialized();

      // 调用真实的巡检API
      const accounts = await this.callGetMobileUsersAPI();

      return accounts;
    } catch (error) {
      console.error('获取账户数据失败:', error);
      throw error;
    }
  }

  /**
   * 提取人员姓名和备注
   * @param fullName 完整姓名（可能包含备注）
   * @returns 分离后的姓名和备注
   */
  private extractPersonNameAndAlias(fullName: string): { personName: string; personAlias?: string } {
    let personName = fullName;
    let personAlias: string | undefined;

    // 如果名称中包含备注（格式为"姓名 (备注)"或"姓名 （备注）"）
    const nameMatch = fullName.match(/^([^(（]+)/);
    if (nameMatch) {
      personName = nameMatch[1].trim();
    }

    // 分别匹配英文括号和中文括号中的备注
    const englishAliasMatch = fullName.match(/\(([^)]+)\)/);
    const chineseAliasMatch = fullName.match(/（([^）]+)）/);

    if (englishAliasMatch) {
      personAlias = englishAliasMatch[1];
    } else if (chineseAliasMatch) {
      personAlias = chineseAliasMatch[1];
    }

    return { personName, personAlias };
  }

  /**
   * 检查人员是否已存在账户
   * @param personName 人员姓名
   * @param personAlias 人员备注
   * @returns 如果存在返回账户信息，否则返回null
   */
  public checkPersonAccountExists(personName: string, personAlias?: string): AccountInfo | null {
    // 确保账户数据已加载
    if (this.state.accounts.length === 0) {
      return null;
    }

    // 检查是否存在相同的人员
    const existingAccount = this.state.accounts.find(account => {
      // 比较人员姓名
      if (account.person_name === personName) {
        // 如果都没有备注，或者备注相同，则认为是同一人
        if (!account.person_alias && !personAlias) {
          return true;
        }
        if (account.person_alias === personAlias) {
          return true;
        }
      }
      return false;
    });

    return existingAccount || null;
  }

  /**
   * 添加账户
   * @param account 账户信息
   */
  public async addAccount(account: Omit<AccountInfo, 'id'> & { password: string }): Promise<AccountInfo> {
    try {
      await this.ensureInitialized();

      // 确保账户数据已加载
      await this.getAccounts();

      this.updateState({
        isLoading: true,
        error: undefined
      });

      // 提取人员姓名和别名进行唯一性检查
      const { personName, personAlias } = this.extractPersonNameAndAlias(account.inspector);

      // 检查人员是否已存在账户
      const existingAccount = this.checkPersonAccountExists(personName, personAlias);
      if (existingAccount) {
        const errorMessage = `人员"${account.inspector}"已存在账户，一个人员只能拥有一个账户`;
        this.updateState({
          error: errorMessage,
          isLoading: false
        });
        throw new Error(errorMessage);
      }

      // 调用真实的巡检API
      const newAccount = await this.callAddMobileUserAPI(account);

      // 检查账户是否已存在，避免重复添加
      const existingAccountIndex = this.state.accounts.findIndex(acc => acc.id === newAccount.id);

      let updatedAccounts: AccountInfo[];
      if (existingAccountIndex >= 0) {
        // 如果账户已存在，替换它（这种情况很少见，但为了健壮性保留）
        console.log(`账户ID ${newAccount.id} 已存在，替换现有账户`);
        updatedAccounts = [...this.state.accounts];
        updatedAccounts[existingAccountIndex] = newAccount;

        // 更新缓存中的账户
        dataManager.updateItemInCache(CACHE_KEYS.INSPECTION_ACCOUNTS, newAccount.id, () => newAccount);
      } else {
        // 正常情况：添加新账户到列表最后面（账户管理保持稳定顺序）
        console.log(`添加新账户ID ${newAccount.id} 到列表底部`);
        updatedAccounts = [...this.state.accounts, newAccount];

        // 增量更新缓存：添加新账户到末尾
        dataManager.addItemToCache(CACHE_KEYS.INSPECTION_ACCOUNTS, newAccount, false); // false表示添加到末尾
      }

      // 更新本地状态
      this.updateState({
        accounts: updatedAccounts,
        isLoading: false
      });

      this.emitter.emit('account-added', newAccount);
      return newAccount;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '添加账户失败';
      this.updateState({
        error: errorMessage,
        isLoading: false
      });
      this.emitter.emit('error', errorMessage);
      throw error;
    }
  }

  /**
   * 更新账户信息
   * @param id 账户ID
   * @param updates 更新内容
   * @param currentPassword 当前密码（用于验证）
   * @param newPassword 新密码（可选）
   */
  public async updateAccount(
    id: string,
    updates: Partial<AccountInfo>,
    currentPassword?: string,
    newPassword?: string
  ): Promise<AccountInfo> {
    try {
      await this.ensureInitialized();

      this.updateState({
        isLoading: true,
        error: undefined
      });

      // 调用真实的更新移动用户API
      const updatedAccount = await this.callUpdateMobileUserAPI(id, updates, currentPassword, newPassword);

      // 增量更新缓存：只更新变化的账户
      dataManager.updateItemInCache(CACHE_KEYS.INSPECTION_ACCOUNTS, id, () => updatedAccount);

      // 更新本地状态
      const updatedAccounts = this.state.accounts.map(account =>
        account.id === id ? updatedAccount : account
      );

      this.updateState({
        accounts: updatedAccounts,
        isLoading: false
      });

      this.emitter.emit('account-updated', updatedAccount);
      return updatedAccount;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '更新账户失败';
      this.updateState({
        error: errorMessage,
        isLoading: false
      });
      this.emitter.emit('error', errorMessage);
      throw error;
    }
  }

  /**
   * 删除账户
   * @param id 账户ID
   */
  public async deleteAccount(id: string): Promise<boolean> {
    try {
      await this.ensureInitialized();

      this.updateState({
        isLoading: true,
        error: undefined
      });

      // 调用真实的删除移动用户API
      const success = await this.callDeleteMobileUserAPI(id);

      if (success) {
        // 增量更新缓存：从缓存中移除账户
        dataManager.removeItemFromCache(CACHE_KEYS.INSPECTION_ACCOUNTS, id);

        // 更新本地状态
        const updatedAccounts = this.state.accounts.filter(account => account.id !== id);
        this.updateState({
          accounts: updatedAccounts,
          isLoading: false
        });

        this.emitter.emit('account-deleted', id);
      } else {
        this.updateState({ isLoading: false });
      }

      return success;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '删除账户失败';
      this.updateState({
        error: errorMessage,
        isLoading: false
      });
      this.emitter.emit('error', errorMessage);
      throw error;
    }
  }

  /**
   * 修改账户密码
   * @param username 用户名
   * @param oldPassword 旧密码
   * @param newPassword 新密码
   */
  public async changePassword(username: string, oldPassword: string, newPassword: string): Promise<boolean> {
    try {
      await this.ensureInitialized();
      
      this.updateState({ 
        isLoading: true,
        error: undefined 
      });

      // TODO: 实现真实的修改密码API
      // 目前暂时返回成功，实际项目中需要调用后端API
      const success = true;

      this.updateState({ isLoading: false });
      this.emitter.emit('password-changed', { username, success });
      return success;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '修改密码失败';
      this.updateState({ 
        error: errorMessage,
        isLoading: false 
      });
      this.emitter.emit('error', errorMessage);
      throw error;
    }
  }

  /**
   * 确保服务已初始化
   */
  private async ensureInitialized(): Promise<void> {
    // 确保基础巡检服务已初始化
    const inspectionService = InspectionService.getInstance();
    await inspectionService.ensureInitialized();
  }

  /**
   * 调用添加移动用户API
   * @param account 账户信息
   */
  private async callAddMobileUserAPI(account: Omit<AccountInfo, 'id'> & { password: string }): Promise<AccountInfo> {
    try {
      // 从巡检人员名称中提取姓名和备注
      const { personName, personAlias } = this.extractPersonNameAndAlias(account.inspector);

      // 构建API参数
      const apiParams = {
        action: 'add_mobile_user',
        action_params: {
          person_name: personName,
          person_alias: personAlias || '', // 如果没有备注，传递空字符串
          user_name: account.contact || '', // 用户名
          password: account.password
        }
      };

      console.log('调用添加移动用户API:', apiParams);

      // 提交任务
      const rawResult = await this.submitTask('DbFun', apiParams);

      // 解析result字段（如果是JSON字符串）
      let result: any;
      if (typeof rawResult === 'string') {
        try {
          result = JSON.parse(rawResult);
        } catch (error) {
          console.error('解析JSON字符串失败:', error);
          throw new Error('API返回数据格式错误');
        }
      } else {
        result = rawResult;
      }

      // 处理API返回结果
      if (result.data && (result.message === 'success' || result.error_code === 'SUCCESS' || result.error_code === 0)) {
        // 成功
        const apiData = result.data;

        // 组合巡检人显示名称（姓名 + 备注）
        const inspectorName = apiData.person_alias && apiData.person_alias !== apiData.person_name
          ? `${apiData.person_name} (${apiData.person_alias})`
          : apiData.person_name;

        // 解析注册时间
        let registerTime: string | undefined = undefined;
        if (apiData.register_timestamp) {
          try {
            // 判断是秒级还是毫秒级时间戳
            const timestamp = typeof apiData.register_timestamp === 'string'
              ? parseInt(apiData.register_timestamp)
              : apiData.register_timestamp;

            // 如果时间戳小于等于10位数，认为是秒级时间戳，需要转换为毫秒
            const milliseconds = timestamp.toString().length <= 10 ? timestamp * 1000 : timestamp;

            const date = new Date(milliseconds);
            if (!isNaN(date.getTime())) {
              // 格式化为 YYYY-MM-DD HH:mm:ss
              const year = date.getFullYear();
              const month = String(date.getMonth() + 1).padStart(2, '0');
              const day = String(date.getDate()).padStart(2, '0');
              const hours = String(date.getHours()).padStart(2, '0');
              const minutes = String(date.getMinutes()).padStart(2, '0');
              const seconds = String(date.getSeconds()).padStart(2, '0');
              registerTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
            }
          } catch (error) {
            console.warn(`解析新添加用户的注册时间失败:`, error);
          }
        }

        const newAccount: AccountInfo = {
          id: apiData.user_id?.toString() || Date.now().toString(),
          department: account.department, // 使用前端传入的完整部门路径
          inspector: inspectorName, // 使用组合后的巡检人名称
          contact: apiData.user_name,
          lastLogin: undefined,
          registerTime: registerTime, // 解析后的注册时间
          passwordExpireDate: this.getDefaultExpireDate(),
          isLocked: false,
          // 保存原始API字段
          person_name: apiData.person_name,
          person_alias: apiData.person_alias,
          person_departments: [account.department], // 使用前端传入的完整部门路径作为数组
          user_name: apiData.user_name,
          user_id: apiData.user_id,
          register_timestamp: apiData.register_timestamp // 保存原始时间戳
        };
        return newAccount;
      } else {
        // 失败
        throw new Error(result.message || `API错误: ${result.error_code}`);
      }
    } catch (error) {
      console.error('添加移动用户API调用失败:', error);
      throw error;
    }
  }

  /**
   * 调用删除移动用户API
   * @param id 账户ID
   */
  private async callDeleteMobileUserAPI(id: string): Promise<boolean> {
    try {
      // 查找要删除的账户信息
      const account = this.state.accounts.find(acc => acc.id === id);
      if (!account) {
        throw new Error(`账户不存在: ${id}`);
      }

      // 确保有必要的删除参数
      if (!account.person_name) {
        throw new Error('缺少人员姓名信息，无法删除账户');
      }

      // 构建API参数
      const apiParams = {
        action: 'delete_mobile_user',
        action_params: {
          person_name: account.person_name,
          person_alias: account.person_alias || '' // 如果没有别名，传递空字符串
        }
      };

      console.log('调用删除移动用户API:', apiParams);

      // 提交任务
      const rawResult = await this.submitTask('DbFun', apiParams);

      // 解析result字段（如果是JSON字符串）
      let result: any;
      if (typeof rawResult === 'string') {
        try {
          result = JSON.parse(rawResult);
        } catch (error) {
          console.error('解析JSON字符串失败:', error);
          throw new Error('API返回数据格式错误');
        }
      } else {
        result = rawResult;
      }

      // 处理API返回结果
      if (result.data && (result.message === 'success' || result.error_code === 'SUCCESS' || result.error_code === 0)) {
        // 成功
        console.log('删除移动用户成功:', result.data);
        return true;
      } else {
        // 失败
        throw new Error(result.message || `API错误: ${result.error_code}`);
      }
    } catch (error) {
      console.error('删除移动用户API调用失败:', error);
      throw error;
    }
  }

  /**
   * 调用更新移动用户API
   * @param id 账户ID
   * @param updates 更新内容
   * @param currentPassword 当前密码
   * @param newPassword 新密码
   */
  private async callUpdateMobileUserAPI(
    id: string,
    updates: Partial<AccountInfo>,
    currentPassword?: string,
    newPassword?: string
  ): Promise<AccountInfo> {
    try {
      // 查找要更新的账户信息
      const account = this.state.accounts.find(acc => acc.id === id);
      if (!account) {
        throw new Error(`账户不存在: ${id}`);
      }

      // 确保有必要的更新参数
      if (!account.person_name || !account.user_name) {
        throw new Error('缺少必要的账户信息，无法更新');
      }

      // 如果没有提供当前密码，抛出错误
      if (!currentPassword) {
        throw new Error('更新账户信息需要提供当前密码进行验证');
      }

      // 直接使用当前账户的巡检人员信息，不允许通过更新参数修改
      const personName = account.person_name;
      const personAlias = account.person_alias || '';

      // 构建API参数
      const apiParams = {
        action: 'update_mobile_user',
        action_params: {
          person_name: personName,
          person_alias: personAlias,
          current_user_name: account.user_name, // 当前用户名
          current_password: currentPassword, // 当前密码
          new_user_name: updates.contact || account.user_name, // 新用户名（如果有更新）
          new_password: newPassword // 新密码（可选）
        }
      };

      console.log('调用更新移动用户API:', apiParams);

      // 提交任务
      const rawResult = await this.submitTask('DbFun', apiParams);

      // 解析result字段（如果是JSON字符串）
      let result: any;
      if (typeof rawResult === 'string') {
        try {
          result = JSON.parse(rawResult);
        } catch (error) {
          console.error('解析JSON字符串失败:', error);
          throw new Error('API返回数据格式错误');
        }
      } else {
        result = rawResult;
      }

      // 处理API返回结果
      if (result.data && (result.message === 'success' || result.error_code === 'SUCCESS' || result.error_code === 0)) {
        // 成功
        const apiData = result.data;

        // 组合巡检人显示名称（姓名 + 备注）
        const inspectorName = apiData.person_alias && apiData.person_alias !== apiData.person_name
          ? `${apiData.person_name} (${apiData.person_alias})`
          : apiData.person_name;

        // 构建更新后的账户对象
        const updatedAccount: AccountInfo = {
          ...account,
          ...updates,
          inspector: inspectorName, // 使用组合后的巡检人名称
          contact: apiData.updated_user_name || updates.contact || account.contact,
          // 更新原始API字段
          person_name: apiData.person_name,
          person_alias: apiData.person_alias,
          user_name: apiData.updated_user_name || account.user_name,
          user_id: apiData.user_id || account.user_id
        };

        console.log('更新移动用户成功:', updatedAccount);
        return updatedAccount;
      } else {
        // 失败
        throw new Error(result.message || `API错误: ${result.error_code}`);
      }
    } catch (error) {
      console.error('更新移动用户API调用失败:', error);
      throw error;
    }
  }

  /**
   * 调用获取移动用户列表API
   */
  private async callGetMobileUsersAPI(): Promise<AccountInfo[]> {
    try {
      // 构建API参数
      const apiParams = {
        action: 'get_mobile_users',
        action_params: {}
      };

      console.log('调用获取移动用户列表API:', apiParams);

      // 提交任务
      const rawResult = await this.submitTask('DbFun', apiParams);

      console.log('获取移动用户列表API原始返回结果:', rawResult);

      // 解析result字段（如果是JSON字符串）
      let result: any;
      if (typeof rawResult === 'string') {
        try {
          result = JSON.parse(rawResult);
          console.log('解析JSON字符串后的结果:', result);
        } catch (error) {
          console.error('解析JSON字符串失败:', error);
          throw new Error('API返回数据格式错误');
        }
      } else {
        result = rawResult;
      }

      console.log('最终处理的结果:', result);

      // 处理API返回结果
      if (result.data && (result.message === 'success' || result.error_code === 'SUCCESS' || result.error_code === 0)) {
        // 成功
        const users = result.data?.users || [];
        const accounts: AccountInfo[] = users.map((user: any, index: number) => {
          // 处理部门信息 - 使用完整的部门路径
          let departmentPath = '未分配部门';

          if (user.person_departments && user.person_departments.length > 0) {
            // 如果有多个部门，取第一个作为主要部门路径
            departmentPath = user.person_departments[0];

            // 如果部门路径不包含分隔符，可能是单个部门名，保持原样
            // 如果包含分隔符（如"/"），则是完整路径，直接使用
            console.log(`用户 ${user.person_name} 的部门路径: ${departmentPath}`);
          }

          // 组合巡检人显示名称（姓名 + 备注）
          const inspectorName = user.person_alias && user.person_alias !== user.person_name
            ? `${user.person_name} (${user.person_alias})`
            : user.person_name;

          // 解析注册时间
          let registerTime: string | undefined = undefined;
          if (user.register_timestamp) {
            try {
              // 判断是秒级还是毫秒级时间戳
              const timestamp = typeof user.register_timestamp === 'string'
                ? parseInt(user.register_timestamp)
                : user.register_timestamp;

              // 如果时间戳小于等于10位数，认为是秒级时间戳，需要转换为毫秒
              const milliseconds = timestamp.toString().length <= 10 ? timestamp * 1000 : timestamp;

              const date = new Date(milliseconds);
              if (!isNaN(date.getTime())) {
                // 格式化为 YYYY-MM-DD HH:mm:ss
                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                const hours = String(date.getHours()).padStart(2, '0');
                const minutes = String(date.getMinutes()).padStart(2, '0');
                const seconds = String(date.getSeconds()).padStart(2, '0');
                registerTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
              }
            } catch (error) {
              console.warn(`解析用户 ${user.person_name} 的注册时间失败:`, error);
            }
          }

          return {
            id: user.user_id.toString(),
            department: departmentPath, // 使用完整的部门路径
            inspector: inspectorName,
            contact: user.user_name,
            lastLogin: undefined, // 后端暂未返回
            registerTime: registerTime, // 解析后的注册时间
            passwordExpireDate: undefined,
            isLocked: false,
            // 保存原始API字段
            person_name: user.person_name,
            person_alias: user.person_alias,
            person_departments: user.person_departments,
            user_name: user.user_name,
            user_id: user.user_id,
            register_timestamp: user.register_timestamp // 保存原始时间戳
          };
        });

        console.log('解析后的账户列表:', accounts);

        // 去重：根据user_id去除重复的账户
        const uniqueAccounts = accounts.reduce((acc: AccountInfo[], current: AccountInfo) => {
          const existingIndex = acc.findIndex(account => account.id === current.id);
          if (existingIndex >= 0) {
            // 如果已存在，保留最新的（通常是后面的）
            console.log(`发现重复账户ID ${current.id}，保留最新版本`);
            acc[existingIndex] = current;
          } else {
            acc.push(current);
          }
          return acc;
        }, []);

        console.log(`原始账户数量: ${accounts.length}, 去重后数量: ${uniqueAccounts.length}`);

        return uniqueAccounts;
      } else {
        // 失败
        throw new Error(result.message || `API错误: ${result.error_code}`);
      }
    } catch (error) {
      console.error('获取移动用户列表API调用失败:', error);
      throw error;
    }
  }







  /**
   * 获取默认密码过期日期（当前日期后30天）
   */
  private getDefaultExpireDate(): string {
    const date = new Date();
    date.setDate(date.getDate() + 30);
    return date.toISOString().split('T')[0];
  }
}

export default InspectionAccountService;
