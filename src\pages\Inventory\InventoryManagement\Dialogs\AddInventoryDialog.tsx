import React, { useState, useEffect, useMemo } from 'react';
import { Alert, Spin } from 'antd';
import { InventoryItem, ExtFieldDefinition } from '../../../../types/inventory';
import InventoryForm from './InventoryForm';
import useDeviceService from '../../../../hooks/Inventory/useDeviceService';
import { useInventory } from '../../../../hooks/Inventory/useInventory';
import DialogBase from '../../../../components/ui/DialogBase';
import { useFormFocus } from '../../../../hooks/base/useFormFocus';

interface AddInventoryDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onAdd: (item: Omit<InventoryItem, 'id'>) => Promise<void>;
  initialCategoryInfo: {
    type: string;
    name: string;
    parentCategory?: string;
    department?: string;
    responsible?: string;
    fromContextMenu?: boolean; // 是否来自右键菜单
  } | null;
  extFields?: ExtFieldDefinition[];  // 扩展字段定义
  extFieldValues?: Record<string, any>;  // 扩展字段值
  onExtFieldChange?: (key: string, value: any) => void;  // 扩展字段变更回调
  disableTypeSelection?: boolean; // 是否禁用设备类型选择，用于从树状图联动添加时
  disableDepartmentSelection?: boolean; // 是否禁用部门选择，用于从树状图联动添加时
  disableResponsibleSelection?: boolean; // 是否禁用责任人选择，用于从树状图联动添加时
}

const AddInventoryDialog: React.FC<AddInventoryDialogProps> = ({
  isOpen,
  onClose,
  onAdd,
  initialCategoryInfo = null,
  extFields = [],
  extFieldValues = {},
  onExtFieldChange,
  disableTypeSelection = false,
  disableDepartmentSelection = false,
  disableResponsibleSelection = false
}) => {
  // 引入设备服务Hook获取加载状态
  const { isLoading, progress } = useDeviceService();
  const { formExtFields, clearFormExtFields } = useInventory();
  const [apiError, setApiError] = useState<string | null>(null);

  // 使用表单焦点管理
  const { formRef, focusFirstError } = useFormFocus({
    autoFocusOnError: true,
    scrollToError: true
  });

  // 在对话框关闭时清除扩展字段
  const handleClose = () => {
    // 清除表单扩展字段
    clearFormExtFields();
    // 调用原有的关闭函数
    onClose();
  };

  // 处理表单提交
  const handleSubmit = async (data: Omit<InventoryItem, 'id'>) => {
    try {
      setApiError(null);

      // 使用原有的onAdd方法更新前端状态
      // 这个方法会调用inventoryService.addInventoryItem，已经包含了向后端发送请求的逻辑
      await onAdd(data);

      // 成功添加后关闭对话框
      onClose();
    } catch (err) {
      console.error('添加设备台账失败:', err);
      setApiError(err instanceof Error ? err.message : '添加设备失败，请稍后重试');
    }
  };

  // 准备初始表单数据，融合当前选中的分类信息（使用useMemo避免无限重新创建）
  const initialFormData = useMemo(() => {
    return initialCategoryInfo ? {
      type: initialCategoryInfo.type || '',
      name: initialCategoryInfo.name || '',
      department: initialCategoryInfo.department || '',
      responsible: initialCategoryInfo.responsible || ''
    } : {};
  }, [initialCategoryInfo]);

  // 只在对话框打开时记录日志，避免每次渲染都输出
  useEffect(() => {
    if (isOpen) {
      console.log('AddInventoryDialog接收到的initialCategoryInfo:', initialCategoryInfo);
      console.log('AddInventoryDialog生成的initialFormData:', initialFormData);
    }
  }, [isOpen, initialCategoryInfo, initialFormData]); // 现在可以安全地包含initialFormData，因为使用了useMemo

  // 如果对话框未打开，则不渲染内容
  if (!isOpen) return null;

  return (
    <DialogBase
      isOpen={isOpen}
      onClose={handleClose}
      width="100%"
      maxWidth="64rem"
      maxHeight="95vh"
      autoFocus={true}
      restoreFocus={true}
      closeOnOverlayClick={false}
    >
      <div className="flex flex-col h-full max-h-[95vh]">
        {/* 对话框标题 - 固定高度 */}
        <div className="flex-shrink-0 flex justify-between items-center px-4 sm:px-6 py-3 border-b">
          <h2 className="text-lg sm:text-xl font-semibold text-gray-800">添加设备台账</h2>
          <button
            onClick={handleClose}
            disabled={isLoading}
            className="text-gray-500 hover:text-gray-700 focus-ring rounded-full p-1 flex-shrink-0"
            aria-label="关闭对话框"
          >
            <svg className="h-5 w-5 sm:h-6 sm:w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 对话框内容 - 可滚动区域 */}
        <div className="flex-1 overflow-y-auto min-h-0 px-4 sm:px-6 py-4">{/* 响应式内边距 */}

          {/* 错误提示 */}
          {apiError && (
            <Alert
              message="错误"
              description={apiError}
              type="error"
              showIcon
              className="mb-3 sm:mb-4"
              closable
              onClose={() => setApiError(null)}
            />
          )}

          {/* 加载状态 */}
          {isLoading && (
            <div className="mb-3 sm:mb-4 flex items-center space-x-2">
              <Spin />
              <span className="text-blue-600 text-sm sm:text-base">正在处理请求 ({Math.round(progress)}%)</span>
            </div>
          )}

          {/* 表单内容区域 - 不包含按钮 */}
          <div ref={formRef}>
            <InventoryForm
              initialData={initialFormData}
              extFields={formExtFields}
              extFieldValues={extFieldValues}
              onExtFieldChange={onExtFieldChange}
              onSubmit={handleSubmit}
              onCancel={handleClose}
              disabled={isLoading}
              disableTypeSelection={disableTypeSelection && !!initialCategoryInfo?.type}
              disableDepartmentSelection={disableDepartmentSelection && !!initialCategoryInfo?.department}
              disableResponsibleSelection={disableResponsibleSelection && !!initialCategoryInfo?.responsible}
              hideButtons={true} // 新增属性，隐藏表单内的按钮
            />
          </div>
        </div>

        {/* 对话框底部按钮 - 固定在底部，与导出菜单保持一致 */}
        <div className="flex-shrink-0 px-3 sm:px-4 py-2.5 border-t bg-white flex justify-end space-x-2">
          <button
            onClick={handleClose}
            className="px-3 py-1.5 border border-gray-300 rounded text-sm text-gray-700 hover:bg-gray-50 transition-colors"
            disabled={isLoading}
          >
            取消
          </button>
          <button
            onClick={() => {
              // 触发表单提交 - 使用ref确保选择正确的表单
              const form = formRef.current?.querySelector('form');
              if (form) {
                form.requestSubmit();
              }
            }}
            className="px-3 py-1.5 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            disabled={isLoading}
          >
            保存
          </button>
        </div>
      </div>
    </DialogBase>
  );
};

export default AddInventoryDialog;