import React, { useState, useEffect } from 'react';
import { X, AlertCircle } from 'lucide-react';
import { ValidatedInput } from '../../../../components/ui/FormComponents';

interface CreateBackupDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (backupPath: string, backupName?: string) => Promise<void>;
  isCreating?: boolean;
}

export const CreateBackupDialog: React.FC<CreateBackupDialogProps> = ({
  isOpen,
  onClose,
  onConfirm,
  isCreating = false,
}) => {
  const [backupName, setBackupName] = useState('');
  const [error, setError] = useState('');

  // 重置表单
  useEffect(() => {
    if (isOpen) {
      setBackupName('');
      setError('');
    }
  }, [isOpen]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setError('');
      // 传递空字符串作为路径参数
      await onConfirm('', backupName.trim() || undefined);
      onClose();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '创建备份失败';
      setError(errorMessage);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md mx-4">
        {/* 标题栏 */}
        <div className="flex items-center justify-between px-6 py-2.5 border-b">
          <h3 className="text-lg font-semibold text-gray-900">创建数据库备份</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
            disabled={isCreating}
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* 内容 */}
        <form onSubmit={handleSubmit} className="p-6">
          <div className="space-y-4">
            {/* 备份备注 */}
            <div>
              <ValidatedInput
                label="备份备注"
                value={backupName}
                onChange={setBackupName}
                placeholder="例如: 版本发布前备份"
                required={false}
                disabled={isCreating}
                maxByteLength={260}
                showCounter={true}
              />
              <p className="mt-1 text-xs text-gray-500">
                为备份设置一个易于识别的名称，留空则使用默认名称
              </p>
            </div>
          </div>

          {/* 错误提示 */}
          {error && (
            <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
              <div className="flex items-center">
                <AlertCircle className="w-4 h-4 text-red-500 mr-2" />
                <span className="text-sm text-red-700">{error}</span>
              </div>
            </div>
          )}

          {/* 按钮组 */}
          <div className="flex justify-end space-x-3 mt-6">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
              disabled={isCreating}
            >
              取消
            </button>
            <button
              type="submit"
              disabled={isCreating}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 rounded-md transition-colors inline-flex items-center"
            >
              {isCreating && (
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2" />
              )}
              {isCreating ? '创建中...' : '创建备份'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};