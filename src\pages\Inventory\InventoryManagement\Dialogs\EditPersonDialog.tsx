import React, { useState, useEffect } from 'react';
import { PersonDetailInfo } from '../../../../types/inventory';
import DepartmentService from '../../../../services/Inventory/departmentService';
import DepartmentTreeSelect from '../../../../components/DepartmentTreeSelect';
import SecurityLevelSelect from '../../../../components/ui/SecurityLevelSelect';
import { useValidatedFields } from '../../../../hooks/base/useValidatedField';
import { ValidatedInput } from '../../../../components/ui/FormComponents';
import { validatePersonName, validatePersonAlias, validateMobileNumber } from '../../../../utils/fieldValidation';
import { TreeUtils } from '../../../../services/Inventory/department/treeUtils';

interface EditPersonDialogProps {
  isOpen: boolean;
  onClose: () => void;
  personId: string; // 人员节点ID，格式为 "person-{id}"
  onUpdate: (updatedPersonInfo?: any, departmentChanged?: boolean, oldDepartmentId?: string, newDepartmentId?: string) => void; // 更新完成后的回调
}

/**
 * 编辑人员详细信息对话框
 */
const EditPersonDialog: React.FC<EditPersonDialogProps> = ({
  isOpen,
  onClose,
  personId,
  onUpdate
}) => {
  const [personInfo, setPersonInfo] = useState<PersonDetailInfo | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [departmentOptions, setDepartmentOptions] = useState<any[]>([]);
  const [fieldErrors, setFieldErrors] = useState<Record<string, string | undefined>>({});

  // 表单状态
  const [formData, setFormData] = useState({
    user_name: '',
    alias: '',
    mobile_number: '',
    position_security_level: undefined as number | undefined,
    department_id: '', // 当前选择的部门
    department_name: '',
    department_parent_name: ''
  });

  // 使用多字段验证Hook
  const validationConfig = {
    user_name: 'personName',
    alias: 'personAlias',
    mobile_number: 'mobileNumber'
  };

  const { getFieldProps } = useValidatedFields(
    {
      user_name: formData.user_name,
      alias: formData.alias,
      mobile_number: formData.mobile_number
    },
    {
      user_name: (value) => handleFieldChange('user_name', value),
      alias: (value) => handleFieldChange('alias', value),
      mobile_number: (value) => handleFieldChange('mobile_number', value)
    },
    validationConfig
  );

  // 当前部门信息（从personId中提取）
  const [currentDepartment, setCurrentDepartment] = useState<{
    id: number;
    name: string;
  } | null>(null);

  // 从人员ID中提取数字ID和部门ID（支持新旧格式）
  const extractPersonId = (nodeId: string): number | null => {
    // 新格式：person-1-dept-2 -> 1
    // 旧格式：person-1 -> 1
    const match = nodeId.match(/^person-(\d+)/);
    return match ? parseInt(match[1], 10) : null;
  };

  // 从人员ID中提取当前部门ID
  const extractCurrentDepartmentId = (nodeId: string): number | null => {
    // 新格式：person-1-dept-2 -> 2
    const match = nodeId.match(/^person-\d+-dept-(\d+)$/);
    return match ? parseInt(match[1], 10) : null;
  };

  // 从人员节点ID中提取部门节点ID
  const extractDepartmentNodeId = (personNodeId: string): string | null => {
    // 从人员节点ID推断部门节点ID
    // person-1-dept-2 -> dept-2
    // person-1 -> 需要从树中查找
    const match = personNodeId.match(/^person-\d+-dept-(\d+)$/);
    if (match) {
      return `dept-${match[1]}`;
    }

    // 如果是简单格式，需要从树中查找该人员所在的部门
    const departmentService = DepartmentService.getInstance();
    const departmentCategories = departmentService.getState().departmentCategories;

    // 递归查找人员节点所在的部门
    const findPersonDepartment = (categories: any[]): string | null => {
      for (const category of categories) {
        if (category.children) {
          for (const child of category.children) {
            if (child.id === personNodeId) {
              return category.id; // 返回父部门的ID
            }
          }
          // 递归查找
          const found = findPersonDepartment(category.children);
          if (found) return found;
        }
      }
      return null;
    };

    return findPersonDepartment(departmentCategories);
  };

  // 从缓存获取部门列表
  const loadDepartmentsFromCache = () => {
    try {
      const departmentService = DepartmentService.getInstance();
      const departmentCategories = departmentService.getState().departmentCategories;

      // 转换部门树为选择器选项，跳过根节点和人员节点
      const convertToTreeOptions = (categories: any[]): any[] => {
        const options: any[] = [];

        categories.forEach(category => {
          // 跳过根节点（公司名称），直接处理其子部门
          if (category.id === 'all-dept') {
            // 如果是根节点，直接处理其子部门
            if (category.children && category.children.length > 0) {
              const childOptions = convertToTreeOptions(category.children);
              options.push(...childOptions);
            }
          } else if (!category.id.startsWith('person-')) {
            // 跳过人员节点，只处理部门节点
            const option = {
              id: category.id,
              value: category.id,
              label: category.name,
              children: [],
              departmentName: category.name,
              parentDepartmentName: '', // 将在下面设置
              originalDeptData: category
            };

            // 递归处理子部门，过滤掉人员节点
            if (category.children && category.children.length > 0) {
              const filteredChildren = category.children.filter(child => !child.id.startsWith('person-'));
              if (filteredChildren.length > 0) {
                option.children = convertToTreeOptions(filteredChildren);
              }
            }

            options.push(option);
          }
          // 如果是人员节点（category.id.startsWith('person-')），则跳过不处理
        });

        return options;
      };

      // 设置父部门名称的递归函数
      const setParentNames = (options: any[], parentName: string = '') => {
        options.forEach(option => {
          option.parentDepartmentName = parentName;
          if (option.children && option.children.length > 0) {
            setParentNames(option.children, option.departmentName);
          }
        });
      };

      const treeOptions = convertToTreeOptions(departmentCategories);
      setParentNames(treeOptions);
      setDepartmentOptions(treeOptions);

      console.log('部门选择器选项（已排除根节点和人员节点）:', treeOptions);
    } catch (error) {
      console.error('从缓存加载部门列表失败:', error);
    }
  };

  // 从缓存获取当前部门的父部门名称
  const getCurrentDepartmentParentName = (departmentId: number): string => {
    try {
      const departmentService = DepartmentService.getInstance();
      const departmentCategories = departmentService.getState().departmentCategories;

      // 递归查找部门节点
      const findDepartmentById = (categories: any[], targetId: number): any => {
        for (const category of categories) {
          // 检查当前节点（跳过根节点）
          if (category.id !== 'all-dept' && category.id.includes('dept-')) {
            const deptId = parseInt(category.id.replace('dept-', ''));
            if (deptId === targetId) {
              return category;
            }
          }

          // 递归检查子节点
          if (category.children && category.children.length > 0) {
            const found = findDepartmentById(category.children, targetId);
            if (found) return found;
          }
        }
        return null;
      };

      // 查找父部门
      const findParentDepartment = (categories: any[], targetId: number): any => {
        for (const category of categories) {
          if (category.children && category.children.length > 0) {
            for (const child of category.children) {
              if (child.id !== 'all-dept' && child.id.includes('dept-')) {
                const childDeptId = parseInt(child.id.replace('dept-', ''));
                if (childDeptId === targetId) {
                  return category;
                }
              }
            }
            // 递归查找
            const found = findParentDepartment(category.children, targetId);
            if (found) return found;
          }
        }
        return null;
      };

      const parentDepartment = findParentDepartment(departmentCategories, departmentId);
      return parentDepartment && parentDepartment.id !== 'all-dept' ? parentDepartment.name : '';
    } catch (error) {
      console.error('从缓存获取当前部门父部门名称失败:', error);
      return '';
    }
  };

  // 获取人员详细信息
  const fetchPersonInfo = async () => {
    const numericId = extractPersonId(personId);
    if (!numericId) {
      setError('无效的人员ID');
      return;
    }

    // 获取当前部门ID
    const currentDeptId = extractCurrentDepartmentId(personId);

    try {
      setIsLoading(true);
      setError(null);
      const departmentService = DepartmentService.getInstance();

      // 使用缓存机制获取人员信息
      const person = await departmentService.getPersonInfo(numericId);

      if (person) {
        setPersonInfo(person);

        // 确定当前部门信息
        let selectedDepartment = null;

        // 首先尝试从人员节点ID中获取部门信息
        const departmentNodeId = extractDepartmentNodeId(personId);

        if (currentDeptId && person.departments) {
          // 从人员的部门列表中找到当前部门
          selectedDepartment = person.departments.find((dept: any) => dept.id === currentDeptId);
        }

        // 如果没有找到当前部门，但有部门节点ID，尝试从树中获取部门信息
        if (!selectedDepartment && departmentNodeId) {
          const departmentService = DepartmentService.getInstance();
          const departmentCategories = departmentService.getState().departmentCategories;
          const departmentNode = departmentCategories.find((cat: any) =>
            TreeUtils.findNodeById([cat], departmentNodeId)
          );

          if (departmentNode) {
            const foundDeptNode = TreeUtils.findNodeById([departmentNode], departmentNodeId);
            if (foundDeptNode) {
              // 从部门节点创建部门信息
              const deptIdMatch = departmentNodeId.match(/^dept-(.+)$/);
              let deptId: number | string = 0;

              if (deptIdMatch) {
                const idStr = deptIdMatch[1];
                if (idStr.startsWith('temp-')) {
                  // 临时ID，使用字符串
                  deptId = idStr;
                } else {
                  // 真实ID，转换为数字
                  deptId = parseInt(idStr, 10);
                }
              }

              selectedDepartment = {
                id: deptId,
                name: foundDeptNode.name,
                path_name: foundDeptNode.departmentPath || foundDeptNode.name,
                is_primary: 1
              };

              console.log('从树中获取部门信息:', selectedDepartment);
            }
          }
        }

        // 如果仍然没有找到当前部门，使用主要部门
        if (!selectedDepartment && person.departments && person.departments.length > 0) {
          selectedDepartment = person.departments.find((dept: any) => dept.is_primary === 1) || person.departments[0];
        }

        // 设置当前部门信息
        if (selectedDepartment) {
          setCurrentDepartment({
            id: selectedDepartment.id,
            name: selectedDepartment.name
          });
        }

        // 获取当前部门的父部门名称
        let currentDepartmentParentName = '';
        if (selectedDepartment) {
          // 如果是数字ID，正常获取父部门名称
          if (typeof selectedDepartment.id === 'number') {
            currentDepartmentParentName = getCurrentDepartmentParentName(selectedDepartment.id);
          } else {
            // 如果是临时ID，从树中获取父部门名称
            const deptNodeId = departmentNodeId || extractDepartmentNodeId(personId);
            if (deptNodeId) {
              const departmentService = DepartmentService.getInstance();
              const departmentCategories = departmentService.getState().departmentCategories;

              // 查找父部门
              const findParentDepartment = (categories: any[], targetId: string): any => {
                for (const category of categories) {
                  if (category.children) {
                    for (const child of category.children) {
                      if (child.id === targetId) {
                        return category;
                      }
                    }
                    // 递归查找
                    const found = findParentDepartment(category.children, targetId);
                    if (found) return found;
                  }
                }
                return null;
              };

              const parentDept = findParentDepartment(departmentCategories, deptNodeId);
              if (parentDept && parentDept.id !== 'all-dept') {
                currentDepartmentParentName = parentDept.name;
              }
            }
          }
        }

        // 构建部门ID字符串
        let departmentIdStr = '';
        if (selectedDepartment) {
          if (typeof selectedDepartment.id === 'number') {
            departmentIdStr = `dept-${selectedDepartment.id}`;
          } else {
            departmentIdStr = `dept-${selectedDepartment.id}`;
          }
        }

        setFormData({
          user_name: person.user_name || '',
          alias: person.alias || '',
          mobile_number: person.mobile_number || '',
          position_security_level: person.position_security_level || 0,
          department_id: departmentIdStr,
          department_name: selectedDepartment ? selectedDepartment.name : '',
          department_parent_name: currentDepartmentParentName // 设置当前部门的父部门名称
        });
      } else {
        setError('未找到人员信息');
      }
    } catch (error) {
      console.error('获取人员信息失败:', error);
      setError('获取人员信息失败');
    } finally {
      setIsLoading(false);
    }
  };

  // 更新后刷新人员信息并更新缓存
  const refreshPersonInfoAfterUpdate = async (): Promise<any | null> => {
    try {
      console.log('刷新更新后的人员信息');

      const departmentService = DepartmentService.getInstance();

      // 强制刷新人员缓存以获取最新数据
      const refreshSuccess = await departmentService.refreshPersonCache();

      if (refreshSuccess && personInfo?.id) {
        // 从刷新后的缓存中获取最新的人员信息
        const updatedPersonInfo = await departmentService.getPersonInfo(personInfo.id);

        if (updatedPersonInfo) {
          console.log('获取到更新后的人员信息:', updatedPersonInfo);

          // 更新当前对话框中的人员信息显示
          setPersonInfo(updatedPersonInfo);
          console.log('对话框中的人员信息已更新');

          return updatedPersonInfo;
        } else {
          console.warn('未能从缓存中获取更新后的人员信息');
        }
      } else {
        console.warn('刷新人员缓存失败');
      }
    } catch (error) {
      console.error('刷新更新后的人员信息失败:', error);
      // 即使刷新失败也不影响主流程，只是缓存可能不是最新的
    }

    return null;
  };

  // 保存人员信息
  const handleSave = async () => {
    if (!personInfo || !currentDepartment) return;

    // 表单验证
    const nameValidation = validatePersonName(formData.user_name);
    const aliasValidation = validatePersonAlias(formData.alias);
    const mobileValidation = validateMobileNumber(formData.mobile_number);

    if (!nameValidation.isValid) {
      setError(nameValidation.error || '人员名称验证失败');
      return;
    }

    if (!aliasValidation.isValid) {
      setError(aliasValidation.error || '人员备注验证失败');
      return;
    }

    if (!mobileValidation.isValid) {
      setError(mobileValidation.error || '联系方式验证失败');
      return;
    }

    // 岗位密级必填验证 - 修复：0是有效值（非涉密人员）
    if (typeof formData.position_security_level !== 'number') {
      setError('岗位密级不能为空');
      return;
    }

    try {
      setIsSaving(true);
      setError(null);

      // 创建路径解析器并获取当前部门路径
      const departmentService = DepartmentService.getInstance();
      const pathResolver = await departmentService.createPathResolver();

      // 查找当前部门节点
      const departmentCategories = departmentService.getState().departmentCategories;
      const findDepartmentById = (categories: any[], targetId: string): any => {
        for (const category of categories) {
          if (category.id === targetId) {
            return category;
          }
          if (category.children && category.children.length > 0) {
            const found = findDepartmentById(category.children, targetId);
            if (found) return found;
          }
        }
        return null;
      };

      const currentDepartmentNode = findDepartmentById(departmentCategories, `dept-${currentDepartment.id}`);
      if (!currentDepartmentNode) {
        throw new Error(`未找到当前部门节点: dept-${currentDepartment.id}`);
      }

      const currentDepartmentPath = pathResolver.getApiPath(currentDepartmentNode);

      // 构建更新参数，按照最新的API格式
      const params: any = {
        action: 'update_person',
        action_params: {
          user_name: personInfo.user_name, // 原姓名
          alias: personInfo.alias || '', // 原别名
          current_department_path: currentDepartmentPath, // 当前部门路径
          new_user_name: formData.user_name, // 新姓名
          new_alias: formData.alias, // 新别名
          mobile_number: formData.mobile_number, // 新联系方式
          position_security_level: formData.position_security_level // 新岗位密级
        }
      };

      // 如果部门发生了变化，添加新部门路径参数
      if (formData.department_name && formData.department_name !== currentDepartment.name) {
        const newDepartment = findDepartmentById(departmentCategories, formData.department_id);
        if (newDepartment) {
          const newDepartmentPath = pathResolver.getApiPath(newDepartment);
          params.action_params.department_path = newDepartmentPath; // 新部门路径
        }
      }

      console.log('更新人员信息:', JSON.stringify(params));

      // 提交更新请求
      const result = await departmentService.submitTask('DbFun', params);

      if (result && result.success) {
        console.log('人员信息更新成功，开始查询最新信息');

        // 检查部门是否发生了变化
        const departmentChanged = formData.department_name && formData.department_name !== currentDepartment.name;
        const oldDepartmentId = departmentChanged ? `dept-${currentDepartment.id}` : undefined;
        const newDepartmentId = departmentChanged ? formData.department_id : undefined;

        console.log('部门变化检查:', {
          departmentChanged,
          oldDepartment: currentDepartment.name,
          newDepartment: formData.department_name,
          oldDepartmentId,
          newDepartmentId
        });

        // 更新成功后，查询该人员的最新信息并更新缓存
        const updatedPersonInfo = await refreshPersonInfoAfterUpdate();

        // 通知父组件更新，传递更新后的人员信息和部门变化信息
        if (updatedPersonInfo) {
          onUpdate(updatedPersonInfo, departmentChanged, oldDepartmentId, newDepartmentId);
        } else {
          onUpdate(undefined, departmentChanged, oldDepartmentId, newDepartmentId);
        }

        onClose(); // 关闭对话框
      } else {
        setError('更新人员信息失败');
      }
    } catch (error) {
      console.error('更新人员信息失败:', error);
      setError(error instanceof Error ? error.message : '更新人员信息失败');
    } finally {
      setIsSaving(false);
    }
  };

  // 处理表单字段变化
  const handleFieldChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // 实时验证
    if (field === 'user_name') {
      const validation = validatePersonName(value);
      setFieldErrors(prev => ({
        ...prev,
        user_name: validation.isValid ? undefined : validation.error
      }));
    } else if (field === 'alias') {
      const validation = validatePersonAlias(value);
      setFieldErrors(prev => ({
        ...prev,
        alias: validation.isValid ? undefined : validation.error
      }));
    } else if (field === 'mobile_number') {
      const validation = validateMobileNumber(value);
      setFieldErrors(prev => ({
        ...prev,
        mobile_number: validation.isValid ? undefined : validation.error
      }));
    }
  };

  // 处理部门选择变化
  const handleDepartmentChange = (departmentId: string) => {
    // 查找选中的部门信息
    const findDepartmentInfo = (options: any[], targetId: string): any => {
      for (const option of options) {
        if (option.value === targetId) {
          return option;
        }
        if (option.children && option.children.length > 0) {
          const found = findDepartmentInfo(option.children, targetId);
          if (found) return found;
        }
      }
      return null;
    };

    const selectedDept = findDepartmentInfo(departmentOptions, departmentId);
    if (selectedDept) {
      setFormData(prev => ({
        ...prev,
        department_id: departmentId,
        department_name: selectedDept.departmentName,
        department_parent_name: selectedDept.parentDepartmentName
      }));
    }
  };

  // 获取岗位密级显示文本
  const getSecurityLevelText = (level: number): string => {
    switch (level) {
      case 0: return '非涉密人员';
      case 1: return '一般涉密人员';
      case 2: return '重要涉密人员';
      case 3: return '核心涉密人员';
      default: return '未知';
    }
  };

  // 对话框打开时获取人员信息和部门列表
  useEffect(() => {
    if (isOpen && personId) {
      fetchPersonInfo();
      loadDepartmentsFromCache(); // 使用缓存加载部门列表
    }
  }, [isOpen, personId]);

  // 添加键盘事件监听，按ESC键关闭对话框
  useEffect(() => {
    if (isOpen) {
      const handleKeyDown = (e: KeyboardEvent) => {
        if (e.key === 'Escape') {
          onClose();
        }
      };

      window.addEventListener('keydown', handleKeyDown);
      return () => {
        window.removeEventListener('keydown', handleKeyDown);
      };
    }
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md max-h-[90vh] overflow-auto">
        <div className="p-6">
          {/* 对话框标题 */}
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold text-gray-800">编辑人员信息</h2>
            <button
              onClick={onClose}
              disabled={isSaving}
              className="text-gray-500 hover:text-gray-700 focus:outline-none"
            >
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* 错误提示 */}
          {error && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-red-600 text-sm">{error}</p>
            </div>
          )}

          {/* 加载状态 */}
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-3 text-gray-600">加载中...</span>
            </div>
          ) : personInfo ? (
            <form onSubmit={(e) => { e.preventDefault(); handleSave(); }} className="space-y-4">
              {/* 姓名 */}
              <ValidatedInput
                label="姓名"
                {...getFieldProps('user_name')}
                placeholder="请输入姓名"
                disabled={isSaving}
                preset="name"
              />

              {/* 人员备注 */}
              <ValidatedInput
                label="人员备注"
                {...getFieldProps('alias')}
                placeholder="人员备注会以括号形式在人员姓名后面"
                disabled={isSaving}
                preset="alias"
              />

              {/* 所属部门 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  所属部门 <span className="text-red-500">*</span>
                </label>
                <DepartmentTreeSelect
                  options={departmentOptions}
                  value={formData.department_id}
                  onChange={handleDepartmentChange}
                  placeholder="请选择所属部门"
                  disabled={isSaving}
                  className="w-full"
                />
              </div>

              {/* 岗位密级 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  岗位密级 <span className="text-red-500">*</span>
                </label>
                <SecurityLevelSelect
                  value={formData.position_security_level}
                  onChange={(value) => handleFieldChange('position_security_level', value)}
                  disabled={isSaving}
                  className="w-full"
                  required={true}
                />
              </div>

              {/* 联系方式 */}
              <ValidatedInput
                label="联系方式"
                {...getFieldProps('mobile_number')}
                placeholder="请输入联系方式"
                disabled={isSaving}
                preset="mobile"
              />

              {/* 按钮 */}
              <div className="flex justify-end space-x-4 mt-6">
                <button
                  type="button"
                  onClick={onClose}
                  className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  disabled={isSaving}
                >
                  取消
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-blue-600 rounded-md text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
                  disabled={isSaving}
                >
                  {isSaving ? '保存中...' : '保存'}
                </button>
              </div>
            </form>
          ) : (
            <div className="text-center py-8">
              <span className="text-gray-600">暂无人员信息</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default EditPersonDialog;
